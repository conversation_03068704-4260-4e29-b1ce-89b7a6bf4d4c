<section class="relative">
  <div class="w-full h-full">
    <div
      class="mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:px-8 lg:py-20 bg-transparent rounded-t-[48px]"
    >
      <div class="py-12">
        <div>
          <h2
            style="display: flex; overflow: hidden"
            role="heading"
            class="md:text-[6rem] text-[2rem] font-bold md:leading-10 md:pb-14 tracking-tight text-orange-100 md:py-8"
          >
            <span style="transform: translateY(0%) translateZ(0px)">F</span
            ><span style="transform: translateY(0%) translateZ(0px)">r</span
            ><span style="transform: translateY(0%) translateZ(0px)">e</span
            ><span style="transform: translateY(0%) translateZ(0px)">q</span
            ><span style="transform: translateY(0%) translateZ(0px)">u</span
            ><span style="transform: translateY(0%) translateZ(0px)">e</span
            ><span style="transform: translateY(0%) translateZ(0px)">n</span
            ><span style="transform: translateY(0%) translateZ(0px)">t</span
            ><span style="transform: translateY(0%) translateZ(0px)">l</span
            ><span style="transform: translateY(0%) translateZ(0px)">y</span
            ><span style="transform: translateY(0%) translateZ(0px)"
              >&nbsp;</span
            ><span style="transform: translateY(0%) translateZ(0px)">a</span
            ><span style="transform: translateY(0%) translateZ(0px)">s</span
            ><span style="transform: translateY(0%) translateZ(0px)">k</span
            ><span style="transform: translateY(0%) translateZ(0px)">e</span
            ><span style="transform: translateY(0%) translateZ(0px)">d</span
            ><span style="transform: translateY(0%) translateZ(0px)"
              >&nbsp;</span
            >
          </h2>
        </div>
        <div>
          <h2
            style="display: flex; overflow: hidden"
            role="heading"
            class="md:text-[6rem] text-[2rem] font-bold md:leading-10 tracking-tight text-orange-100 md:py-8 font-brand"
          >
            <span style="transform: translateY(0%) translateZ(0px)">q</span
            ><span style="transform: translateY(0%) translateZ(0px)">u</span
            ><span style="transform: translateY(0%) translateZ(0px)">e</span
            ><span style="transform: translateY(0%) translateZ(0px)">s</span
            ><span style="transform: translateY(0%) translateZ(0px)">t</span
            ><span style="transform: translateY(0%) translateZ(0px)">i</span
            ><span style="transform: translateY(0%) translateZ(0px)">o</span
            ><span style="transform: translateY(0%) translateZ(0px)">n</span
            ><span style="transform: translateY(0%) translateZ(0px)">s</span
            ><span style="transform: translateY(0%) translateZ(0px)">.</span
            ><span style="transform: translateY(0%) translateZ(0px)"
              >&nbsp;</span
            >
          </h2>
        </div>
      </div>
      <div
        class="absolute inset-0 -z-10 h-full w-full items-center px-5 py-24 [background:radial-gradient(125%_125%_at_50%_10%,#000_50%,#FF7C33_100%)]"
      ></div>
      <div class="md:mx-auto">
        <div
          class="w-full md:space-y-9 bg-black/10 rounded-xl border border-orange-50/20 text-white backdrop-blur"
          data-orientation="vertical"
        >
          <div
            data-state="closed"
            data-orientation="vertical"
            class="border-x border-b-0 border-black/10 rounded-md md:px-4"
          >
            <h3 data-orientation="vertical" data-state="closed" class="flex">
              <button
                type="button"
                aria-controls="radix-:R4uqupla:"
                aria-expanded="false"
                data-state="closed"
                data-orientation="vertical"
                id="radix-:Ruqupla:"
                class="flex flex-1 items-center justify-between py-4 transition-all [&amp;[data-state=open]&gt;svg]:rotate-180 text-xl md:text-3xl text-left pr-4 md:pr-0 font-medium"
                data-radix-collection-item=""
              >
                <span class="px-6 md:px-2"
                  >Why not just hire a full-time design engineer?</span
                ><svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="lucide lucide-arrow-down-left-square h-12 w-12 shrink-0 transition-transform duration-200 fill-orange-200 stroke-black"
                >
                  <rect width="18" height="18" x="3" y="3" rx="2"></rect>
                  <path d="m16 8-8 8"></path>
                  <path d="M16 16H8V8"></path>
                </svg>
              </button>
            </h3>
            <div
              data-state="closed"
              id="radix-:R4uqupla:"
              hidden=""
              role="region"
              aria-labelledby="radix-:Ruqupla:"
              data-orientation="vertical"
              class="overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down"
              style="
                --radix-accordion-content-height: var(
                  --radix-collapsible-content-height
                );
                --radix-accordion-content-width: var(
                  --radix-collapsible-content-width
                );
              "
            ></div>
          </div>
          <div
            data-state="closed"
            data-orientation="vertical"
            class="border-x border-b-0 border-black/10 rounded-md px-4"
          >
            <h3 data-orientation="vertical" data-state="closed" class="flex">
              <button
                type="button"
                aria-controls="radix-:R5equpla:"
                aria-expanded="false"
                data-state="closed"
                data-orientation="vertical"
                id="radix-:R1equpla:"
                class="flex flex-1 items-center justify-between py-4 transition-all [&amp;[data-state=open]&gt;svg]:rotate-180 text-xl md:text-3xl text-left pl-2 font-medium"
                data-radix-collection-item=""
              >
                Is there a limit to how many requests I can have?<svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="lucide lucide-arrow-down-left-square h-12 w-12 shrink-0 transition-transform duration-200 fill-orange-200 stroke-black"
                >
                  <rect width="18" height="18" x="3" y="3" rx="2"></rect>
                  <path d="m16 8-8 8"></path>
                  <path d="M16 16H8V8"></path>
                </svg>
              </button>
            </h3>
            <div
              data-state="closed"
              id="radix-:R5equpla:"
              hidden=""
              role="region"
              aria-labelledby="radix-:R1equpla:"
              data-orientation="vertical"
              class="overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down"
              style="
                --radix-accordion-content-height: var(
                  --radix-collapsible-content-height
                );
                --radix-accordion-content-width: var(
                  --radix-collapsible-content-width
                );
              "
            ></div>
          </div>
          <div
            data-state="closed"
            data-orientation="vertical"
            class="border-x border-b-0 border-black/10 rounded-md px-4"
          >
            <h3 data-orientation="vertical" data-state="closed" class="flex">
              <button
                type="button"
                aria-controls="radix-:R5uqupla:"
                aria-expanded="false"
                data-state="closed"
                data-orientation="vertical"
                id="radix-:R1uqupla:"
                class="flex flex-1 items-center justify-between py-4 transition-all [&amp;[data-state=open]&gt;svg]:rotate-180 text-xl md:text-3xl text-left pl-2 font-medium"
                data-radix-collection-item=""
              >
                How long will it take to build a full stack feature?<svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="lucide lucide-arrow-down-left-square h-12 w-12 shrink-0 transition-transform duration-200 fill-orange-200 stroke-black"
                >
                  <rect width="18" height="18" x="3" y="3" rx="2"></rect>
                  <path d="m16 8-8 8"></path>
                  <path d="M16 16H8V8"></path>
                </svg>
              </button>
            </h3>
            <div
              data-state="closed"
              id="radix-:R5uqupla:"
              hidden=""
              role="region"
              aria-labelledby="radix-:R1uqupla:"
              data-orientation="vertical"
              class="overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down"
              style="
                --radix-accordion-content-height: var(
                  --radix-collapsible-content-height
                );
                --radix-accordion-content-width: var(
                  --radix-collapsible-content-width
                );
              "
            ></div>
          </div>
          <div
            data-state="closed"
            data-orientation="vertical"
            class="border-x border-b-0 border-black/10 rounded-md px-4"
          >
            <h3 data-orientation="vertical" data-state="closed" class="flex">
              <button
                type="button"
                aria-controls="radix-:R6equpla:"
                aria-expanded="false"
                data-state="closed"
                data-orientation="vertical"
                id="radix-:R2equpla:"
                class="flex flex-1 items-center justify-between py-4 transition-all [&amp;[data-state=open]&gt;svg]:rotate-180 text-xl md:text-3xl text-left pl-2 font-medium"
                data-radix-collection-item=""
              >
                Who is the team?<svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="lucide lucide-arrow-down-left-square h-12 w-12 shrink-0 transition-transform duration-200 fill-orange-200 stroke-black"
                >
                  <rect width="18" height="18" x="3" y="3" rx="2"></rect>
                  <path d="m16 8-8 8"></path>
                  <path d="M16 16H8V8"></path>
                </svg>
              </button>
            </h3>
            <div
              data-state="closed"
              id="radix-:R6equpla:"
              hidden=""
              role="region"
              aria-labelledby="radix-:R2equpla:"
              data-orientation="vertical"
              class="overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down"
              style="
                --radix-accordion-content-height: var(
                  --radix-collapsible-content-height
                );
                --radix-accordion-content-width: var(
                  --radix-collapsible-content-width
                );
              "
            ></div>
          </div>
          <div
            data-state="closed"
            data-orientation="vertical"
            class="border-x border-b-0 border-black/10 rounded-md px-4"
          >
            <h3 data-orientation="vertical" data-state="closed" class="flex">
              <button
                type="button"
                aria-controls="radix-:R6uqupla:"
                aria-expanded="false"
                data-state="closed"
                data-orientation="vertical"
                id="radix-:R2uqupla:"
                class="flex flex-1 items-center justify-between py-4 transition-all [&amp;[data-state=open]&gt;svg]:rotate-180 text-xl md:text-3xl text-left pl-2 font-medium"
                data-radix-collection-item=""
              >
                What if I want a different tech stack?<svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="lucide lucide-arrow-down-left-square h-12 w-12 shrink-0 transition-transform duration-200 fill-orange-200 stroke-black"
                >
                  <rect width="18" height="18" x="3" y="3" rx="2"></rect>
                  <path d="m16 8-8 8"></path>
                  <path d="M16 16H8V8"></path>
                </svg>
              </button>
            </h3>
            <div
              data-state="closed"
              id="radix-:R6uqupla:"
              hidden=""
              role="region"
              aria-labelledby="radix-:R2uqupla:"
              data-orientation="vertical"
              class="overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down"
              style="
                --radix-accordion-content-height: var(
                  --radix-collapsible-content-height
                );
                --radix-accordion-content-width: var(
                  --radix-collapsible-content-width
                );
              "
            ></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
