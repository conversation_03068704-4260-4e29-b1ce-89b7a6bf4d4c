import React from 'react';

interface PricingPlan {
  name: string;
  price: string;
  description: string;
  gradient: string;
  textColor: string;
  buttonBgColor?: string;
}

interface PricingSectionProps {
  title?: string;
  subtitle?: string;
  plans?: PricingPlan[];
}

const defaultPlans: PricingPlan[] = [
  {
    name: "standard",
    price: "$4999/m",
    description: "We build one feature request at a time. Pause or cancel anytime.",
    gradient: "linear-gradient(135deg, #1c1c1c 0%, #242424 50%, #0a0a0a 100%)",
    textColor: "text-white",
    buttonBgColor: "bg-orange-600"
  },
  {
    name: "goblin +",
    price: "$9999/m",
    description: "Goblin mode. In goblin mode we work faster than you can create a job posting.",
    gradient: "linear-gradient(135deg, #ffd479 0%, #ff5a00 50%, #c24914 100%)",
    textColor: "text-black"
  }
];

const PricingCard: React.FC<{ plan: PricingPlan }> = ({ plan }) => {
  return (
    <div
      className={`relative shadow-sm h-[550px] w-full md:w-[400px] rounded-[28px] border border-black/5 ${plan.textColor}`}
      style={{ backgroundImage: plan.gradient }}
    >
      <div className="absolute inset-0 p-8 flex flex-col justify-between">
        <div className="flex flex-col space-y-3">
          <p className="text-lg font-black leading-[1.2353641176] tracking-wide">
            {plan.name}
          </p>
          <p className="mt-6 flex items-baseline justify-start gap-x-2">
            <span className="text-5xl font-bold tracking-tight">
              {plan.price}
            </span>
            <span className="text-sm font-semibold leading-6 tracking-wide text-neutral-600">
              USD
            </span>
          </p>
          <p className={`text-lg font-semibold leading-[1.2353641176] tracking-wide ${plan.textColor}`}>
            {plan.description}
          </p>
          <p className={`text-3xl font-black tracking-[.007em] mt-2 ${plan.textColor}`}></p>
        </div>
        
        <div className={`${plan.buttonBgColor || ''} w-full absolute bottom-0 left-0 rounded-r-[28px] rounded-b-[28px] py-8 pl-6`}>
          <div className="h-12 duration-400">
            <a
              className="sm:w-btn-md focus:outline-none max-w-full disabled:cursor-not-allowed inline-block group"
              target="_self"
            >
              <div className="relative flex grow">
                <div className="z-10 flex grow">
                  <div className="h-full min-h-[40px] max-h-[40px] flex grow">
                    <svg
                      viewBox="0 0 10 40"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-full min-h-cell-md max-h-cell-md -mr-[1px]"
                    >
                      <path
                        d="M10 40V0H6C2.68629 0 0 2.68629 0 6V34C0 37.3137 2.68629 40 6 40H10Z"
                        className="fill-neutral-950"
                      />
                    </svg>
                    <div className="bg-neutral-950 text-white duration-400 h-full truncate flex grow justify-between items-center">
                      <span className="px-2 justify-between duration-400 flex w-full items-center transition-[padding] ease-in-out">
                        <span className="text-2xl font-brand font-semibold">start</span>
                      </span>
                    </div>
                    <svg
                      viewBox="0 0 18 40"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-full min-h-[40px] max-h-[40px] -ml-[1px]"
                    >
                      <path
                        d="M10.899 0H0V40H2C4.40603 40 6.55968 38.5075 7.4045 36.2547L17.4533 9.45786C19.1694 4.88161 15.7864 0 10.899 0Z"
                        className="fill-neutral-950"
                      />
                    </svg>
                  </div>
                  <div className="h-full min-h-[40px] max-h-[40px] flex grow -ml-1">
                    <svg
                      viewBox="0 0 18 40"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-full min-h-[40px] max-h-[40px] -mr-[1px]"
                    >
                      <path
                        d="M7.101 40H18V0H16C13.594 0 11.4403 1.49249 10.5955 3.74532L0.546698 30.5421C-1.1694 35.1184 2.21356 40 7.101 40Z"
                        className="fill-neutral-950"
                      />
                    </svg>
                    <div className="bg-neutral-950 text-white duration-400 h-full min-h-[40px] max-h-[40px] truncate flex grow justify-between items-center">
                      <span className="px-0 group-hover:px-2 duration-400 flex items-center transition-all ease-in-out">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="lucide lucide-arrow-up-right group-hover:rotate-45 transition-all duration-100 group-hover:h-5 group-hover:w-5 h-4 w-4 stroke-white"
                        >
                          <path d="M7 7h10v10" />
                          <path d="M7 17 17 7" />
                        </svg>
                      </span>
                    </div>
                    <svg
                      viewBox="0 0 10 40"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-full min-h-[40px] max-h-[40px] -ml-[1px]"
                    >
                      <path
                        d="M0 40V0H4C7.31371 0 10 2.68629 10 6V34C10 37.3137 7.31371 40 4 40H0Z"
                        className="fill-neutral-950"
                      />
                    </svg>
                  </div>
                </div>
              </div>
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

const PricingSection: React.FC<PricingSectionProps> = ({
  title = "Memberships levels",
  subtitle = "Choose a plan that's right for you.",
  plans = defaultPlans
}) => {
  return (
    <div className="relative h-full bg-black rounded-t-[4rem]">
      <section id="price">
        <div className="w-full h-full md:h-[900px]">
          <div className="flex py-9 justify-center items-center flex-col">
            <div>
              <h2
                style={{ display: "flex", overflow: "hidden" }}
                role="heading"
                className="text-[2.3rem] font-bold md:text-[6rem] md:font-medium tracking-tighter text-orange-50"
              >
                {title.split('').map((char, index) => (
                  <span
                    key={index}
                    style={{ transform: "translateY(0%) translateZ(0px)" }}
                  >
                    {char === ' ' ? '\u00A0' : char}
                  </span>
                ))}
              </h2>
            </div>
            <h3>
              <span className="tracking-tight pb-3 bg-clip-text font-sans text-transparent bg-gradient-to-t from-neutral-200 to-neutral-300 text-xl sm:text-2xl lg:text-3xl font-bold">
                {subtitle}
              </span>
            </h3>
          </div>
          <div className="flex flex-col md:flex-row items-center justify-center w-full gap-2 px-2">
            {plans.map((plan, index) => (
              <PricingCard key={index} plan={plan} />
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

export default PricingSection;
