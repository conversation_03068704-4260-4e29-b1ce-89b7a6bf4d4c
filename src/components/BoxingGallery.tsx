'use client';

import React from 'react';
import { Carousel, Card } from '@/components/ui/apple-cards-carousel';

interface BoxingGalleryProps {
  title?: string;
  subtitle?: string;
}

const BoxingGallery: React.FC<BoxingGalleryProps> = ({
  title = "Training Gallery",
  subtitle = "See our boxing training in action"
}) => {
  const cards = data.map((card, index) => (
    <Card key={card.src} card={card} index={index} />
  ));

  return (
    <section id="gallery" className="w-full h-full py-20 bg-gradient-to-b from-neutral-50 to-neutral-100 dark:from-neutral-900 dark:to-neutral-800">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-6xl font-bold text-neutral-800 dark:text-neutral-200 mb-4">
            {title}
          </h2>
          <p className="text-xl text-neutral-600 dark:text-neutral-400">
            {subtitle}
          </p>
        </div>
        <Carousel items={cards} />
      </div>
    </section>
  );
};

const DummyContent = ({ imageSrc, title }: { imageSrc: string; title: string }) => {
  return (
    <div className="bg-[#F5F5F7] dark:bg-neutral-800 p-8 md:p-14 rounded-3xl mb-4">
      <p className="text-neutral-600 dark:text-neutral-400 text-base md:text-2xl font-sans max-w-3xl mx-auto">
        <span className="font-bold text-neutral-700 dark:text-neutral-200">
          Professional Boxing Training
        </span>{" "}
        Experience world-class boxing training with Ivan, a master of sport with 17 years of experience. 
        Our training programs are designed for all skill levels, from beginners to professional fighters.
      </p>
      <div className="mt-8">
        <img
          src={imageSrc}
          alt={title}
          height="500"
          width="500"
          className="md:w-1/2 md:h-1/2 h-full w-full mx-auto object-cover rounded-lg"
        />
      </div>
    </div>
  );
};

const data = [
  {
    category: "Training",
    title: "Heavy Bag Training",
    src: "https://images.unsplash.com/photo-1549719386-74dfcbf7dbed?w=800&h=600&fit=crop&crop=center",
    content: <DummyContent imageSrc="https://images.unsplash.com/photo-1549719386-74dfcbf7dbed?w=800&h=600&fit=crop&crop=center" title="Heavy Bag Training" />,
  },
  {
    category: "Technique",
    title: "Pad Work Sessions",
    src: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&h=600&fit=crop&crop=center",
    content: <DummyContent imageSrc="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&h=600&fit=crop&crop=center" title="Pad Work Sessions" />,
  },
  {
    category: "Sparring",
    title: "Ring Training",
    src: "https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=800&h=600&fit=crop&crop=center",
    content: <DummyContent imageSrc="https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=800&h=600&fit=crop&crop=center" title="Ring Training" />,
  },
  {
    category: "Fitness",
    title: "Conditioning Workouts",
    src: "https://images.unsplash.com/photo-1517438476312-10d79c077509?w=800&h=600&fit=crop&crop=center",
    content: <DummyContent imageSrc="https://images.unsplash.com/photo-1517438476312-10d79c077509?w=800&h=600&fit=crop&crop=center" title="Conditioning Workouts" />,
  },
  {
    category: "Youth Training",
    title: "Kids Boxing Classes",
    src: "https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa?w=800&h=600&fit=crop&crop=center",
    content: <DummyContent imageSrc="https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa?w=800&h=600&fit=crop&crop=center" title="Kids Boxing Classes" />,
  },
  {
    category: "Professional",
    title: "Competition Prep",
    src: "https://images.unsplash.com/photo-1581009146145-b5ef050c2e1e?w=800&h=600&fit=crop&crop=center",
    content: <DummyContent imageSrc="https://images.unsplash.com/photo-1581009146145-b5ef050c2e1e?w=800&h=600&fit=crop&crop=center" title="Competition Prep" />,
  },
];

export default BoxingGallery;
