import React from 'react';
import { BentoGrid, BentoGridItem } from '@/components/ui/bento-grid';
import { 
  IconTrophy, 
  IconMedal, 
  IconSchool, 
  IconUsers, 
  IconTarget,
  IconCertificate,
  IconStar,
  IconAward
} from '@tabler/icons-react';

interface AboutIvanProps {
  title?: string;
  subtitle?: string;
}

const AboutIvan: React.FC<AboutIvanProps> = ({
  title = "Meet Ivan",
  subtitle = "Your Professional Boxing Coach"
}) => {
  return (
    <section id="about" className="w-full py-20 bg-white dark:bg-black">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-6xl font-bold text-neutral-800 dark:text-neutral-200 mb-4">
            {title}
          </h2>
          <p className="text-xl text-neutral-600 dark:text-neutral-400 max-w-3xl mx-auto">
            {subtitle} - Master of Sport in Kickboxing with 17 years of experience training fighters at all levels
          </p>
        </div>
        <BentoGrid className="max-w-4xl mx-auto">
          {items.map((item, i) => (
            <BentoGridItem
              key={i}
              title={item.title}
              description={item.description}
              header={item.header}
              icon={item.icon}
              className={i === 3 || i === 6 ? "md:col-span-2" : ""}
            />
          ))}
        </BentoGrid>
      </div>
    </section>
  );
};

const Skeleton = ({ className }: { className?: string }) => (
  <div className={`flex flex-1 w-full h-full min-h-[6rem] rounded-xl bg-gradient-to-br from-neutral-200 dark:from-neutral-900 dark:to-neutral-800 to-neutral-100 ${className}`}></div>
);

const ExperienceHeader = () => (
  <div className="flex flex-1 w-full h-full min-h-[6rem] rounded-xl bg-gradient-to-br from-red-500 to-red-700 relative overflow-hidden">
    <div className="absolute inset-0 bg-black/20"></div>
    <div className="relative z-10 flex items-center justify-center w-full">
      <div className="text-center text-white">
        <div className="text-4xl font-bold">17</div>
        <div className="text-sm">Years Experience</div>
      </div>
    </div>
  </div>
);

const ChampionHeader = () => (
  <div className="flex flex-1 w-full h-full min-h-[6rem] rounded-xl bg-gradient-to-br from-yellow-400 to-yellow-600 relative overflow-hidden">
    <div className="absolute inset-0 bg-black/10"></div>
    <div className="relative z-10 flex items-center justify-center w-full">
      <IconTrophy className="h-12 w-12 text-yellow-900" />
    </div>
  </div>
);

const EducationHeader = () => (
  <div className="flex flex-1 w-full h-full min-h-[6rem] rounded-xl bg-gradient-to-br from-blue-500 to-blue-700 relative overflow-hidden">
    <div className="absolute inset-0 bg-black/20"></div>
    <div className="relative z-10 flex items-center justify-center w-full">
      <IconSchool className="h-12 w-12 text-white" />
    </div>
  </div>
);

const TrainingHeader = () => (
  <div className="flex flex-1 w-full h-full min-h-[6rem] rounded-xl bg-gradient-to-br from-green-500 to-green-700 relative overflow-hidden">
    <div className="absolute inset-0 bg-black/20"></div>
    <div className="relative z-10 flex items-center justify-center w-full">
      <IconUsers className="h-12 w-12 text-white" />
    </div>
  </div>
);

const CertificationHeader = () => (
  <div className="flex flex-1 w-full h-full min-h-[6rem] rounded-xl bg-gradient-to-br from-purple-500 to-purple-700 relative overflow-hidden">
    <div className="absolute inset-0 bg-black/20"></div>
    <div className="relative z-10 flex items-center justify-center w-full">
      <IconCertificate className="h-12 w-12 text-white" />
    </div>
  </div>
);

const SpecializationHeader = () => (
  <div className="flex flex-1 w-full h-full min-h-[6rem] rounded-xl bg-gradient-to-br from-orange-500 to-red-600 relative overflow-hidden">
    <div className="absolute inset-0 bg-black/20"></div>
    <div className="relative z-10 flex items-center justify-center w-full">
      <IconTarget className="h-12 w-12 text-white" />
    </div>
  </div>
);

const AchievementsHeader = () => (
  <div className="flex flex-1 w-full h-full min-h-[6rem] rounded-xl bg-gradient-to-br from-indigo-500 to-purple-600 relative overflow-hidden">
    <div className="absolute inset-0 bg-black/20"></div>
    <div className="relative z-10 flex items-center justify-center w-full">
      <IconAward className="h-12 w-12 text-white" />
    </div>
  </div>
);

const items = [
  {
    title: "Master of Sport",
    description: "Master of Sport in Kickboxing (Ukraine) with 1st rank in Boxing. Highest coaching category certified.",
    header: <ExperienceHeader />,
    icon: <IconMedal className="h-4 w-4 text-neutral-500" />,
  },
  {
    title: "Champion Record",
    description: "Multiple-time Champion of Ukraine Championships and Ukraine Cups. Prize winner of CIS Championship.",
    header: <ChampionHeader />,
    icon: <IconTrophy className="h-4 w-4 text-neutral-500" />,
  },
  {
    title: "Education",
    description: "Higher pedagogical education (KNPU) and Master's degree from Kharkiv Academy of Physical Culture.",
    header: <EducationHeader />,
    icon: <IconSchool className="h-4 w-4 text-neutral-500" />,
  },
  {
    title: "Training Specialization",
    description: "Expert in Kickboxing, Boxing, K-1, and special physical preparation for fighters. Perfect for beginners of any gender and age, as well as professionals.",
    header: <TrainingHeader />,
    icon: <IconUsers className="h-4 w-4 text-neutral-500" />,
  },
  {
    title: "Certifications",
    description: "Highest coaching category with specialized training in Eastern martial arts influence on modern combat sports.",
    header: <CertificationHeader />,
    icon: <IconCertificate className="h-4 w-4 text-neutral-500" />,
  },
  {
    title: "Combat Sports Focus",
    description: "Specialized training in Kickboxing, K-1, Boxing from beginner to professional level, plus special physical preparation.",
    header: <SpecializationHeader />,
    icon: <IconTarget className="h-4 w-4 text-neutral-500" />,
  },
  {
    title: "Regional Team Member",
    description: "Former member of Kharkiv region Kickboxing national team with extensive competition experience and proven track record in developing champions.",
    header: <AchievementsHeader />,
    icon: <IconStar className="h-4 w-4 text-neutral-500" />,
  },
];

export default AboutIvan;
